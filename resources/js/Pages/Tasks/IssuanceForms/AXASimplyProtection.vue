<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker"
import Address from '../../../Components/Form/Address.vue'
import RealEstateRegistry from '../../../Components/Form/RealEstateRegistry.vue'
import PersonalData from '../../../Components/Form/PersonalData.vue'
import AxaHealthQuestionnaire from "@/Components/Form/AxaHealthQuestionnaire.vue";
import Checkbox from "primevue/checkbox";

export default {
    components: {
        Checkbox,
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker,
        Address,
        RealEstateRegistry,
        PersonalData,
        AxaHealthQuestionnaire
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        const form = useForm({

            moduloAdesione: null,
            capitaleAssicurato: null,
            indennitaMensile: null,

            // Pacchetto selezionato
            pacchetto: null,

            // Dati assicurato
            /*datiAssicurato: {
                nome: '',
                cognome: '',
                via: '',
                numVia: '',
                cap: '',
                localita: '',
                provincia: '',
                telefono: '',
                email: ''
            },*/

            durata: null,

            premioUnico: null,

            modalitaPagamento: null,

            healthData: {
                salute1: null,
                salute2: null,
                salute3: null,
                salute4: null,
                salute5: null,
                salute6: null,
                salute7: null,
                salute8: null,
                salute9: null,
                salute10: null,
                salute11: null,
                salute12: null,
                salute13: null,
                salute14: null,
                salute15: null,
            },

            beneficiario: null,
            beneficiarioNomine: [
                {},{}
            ],
            referenteTerzo: {
                nome: '',
                cognome: '',
                via: '',
                numVia: '',
                cap: '',
                localita: '',
                provincia: '',
                telefono: '',
                email: ''
            },

            PEP: null,
            PEPType: null,
            congelamentoFondi: null,
            congelamentoFondiMotivo: null,
            precedentiPenali: null,
            precedentiPenaliMotivo: null,
            personaFisica: null,
            origineFondi: null,
            origineFondiAltro: null,

            esigenze2_1: null,
            esigenze2_2: null,
            esigenze3: null,
            esigenze4: null,
            esigenze5: null,
            esigenze6: null,
        });

        // DEBUG: valorizza con dati di esempio
        if (import.meta.env.DEV) {
            Object.assign(form, {
                moduloAdesione: 123456,
                capitaleAssicurato: 125000,
                indennitaMensile: 1200,
                pacchetto: 1,
                /*datiAssicurato: {
                    nome: 'Mario',
                    cognome: 'Debug',
                    via: 'Via Test',
                    numVia: '1',
                    cap: '20100',
                    localita: 'Milano',
                    provincia: 'MI',
                    telefono: '**********',
                    email: '<EMAIL>'
                },*/
                durata: 5,
                premioUnico: 1000,
                modalitaPagamento: 1,
                healthData: {
                    salute1: 0,
                    salute2: 0,
                    salute3: 0,
                    salute4: 0,
                    salute5: 0,
                    salute6: 0,
                    salute7: 0,
                    salute8: 0,
                    salute9: 0,
                    salute10: 0,
                    salute11: 0,
                    salute12: 0,
                    salute13: 0,
                    salute14: 0,
                    salute15: 0,
                }
            });
        }

        return {
            form,
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        },


    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p v-if="issuance.product.processType === 'direct'" class="text-gray-500 text-sm font-normal">Modulo di adesione</p>
                                <p v-else class="text-gray-500 text-sm font-normal">Modulo di raccolta dati</p>
                            </span>
                        </h2>

                        <hr class="my-4">

<!--                        <h2 class="mb-2 mt-14">Assicurato</h2>

                        <PersonalData
                            :personalData="form.datiAssicurato"
                            :errors="{messages: form.errors, key: 'datiAssicurato'}"
                        />-->

                        <h2 class="mb-4 mt-14">POLIZZA CONVENZIONE N. 10122 – N. 120184</h2>

                        <div class="mb-3">
                            <InputText class="w-3/6" v-model="form.moduloAdesione" placeholder="Modulo di Adesione / Certificato di Polizza n." />
                            <FormMessage :errors="form.errors" field="moduloAdesione">Inserisci il Modulo di Adesione / Certificato di Polizza n.</FormMessage>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <InputNumber mode="currency" currency="EUR" locale="it-IT" class="w-full" v-model="form.capitaleAssicurato" placeholder="Capitale Assicurato" />
                                <FormMessage :errors="form.errors" field="capitaleAssicurato">Inserisci il capitale Assicurato</FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputNumber mode="currency" currency="EUR" locale="it-IT" class="w-full" v-model="form.indennitaMensile" placeholder="Indennità mensile" />
                                <FormMessage :errors="form.errors" field="indennitaMensile">Inserisci l'indennità mensile</FormMessage>
                            </div>
                        </div>

                        <h2 class="mb-4 mt-14">Coperture assicurative ed indennizzi</h2>

                        <!-- Tabella dei pacchetti assicurativi -->
                        <div class="border rounded-lg overflow-hidden">
                            <!-- Header della tabella -->
                            <div class="bg-blue-100 grid grid-cols-3 border-b">
                                <div class="p-3 font-semibold text-center border-r">Pacchetti Assicurativi</div>
                                <div class="p-3 font-semibold text-center border-r">Garanzie prestate</div>
                                <div class="p-3 font-semibold text-center">Prestazione</div>
                            </div>

                            <!-- Pacchetto 1 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-1"
                                        :value="1"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-1">
                                        <strong>Pacchetto 1</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    Decesso
                                </div>
                                <div class="p-3">
                                    Capitale Assicurato
                                </div>
                            </div>

                            <!-- Pacchetto 2 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-2"
                                        :value="2"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-2">
                                        <strong>Pacchetto 2</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div>Invalidità Totale Permanente<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div>Capitale Assicurato</div>
                                </div>
                            </div>

                            <!-- Pacchetto 3 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-3"
                                        :value="3"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-3">
                                        <strong>Pacchetto 3</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Lavoratori Dipendenti Privati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div>Perdita d'Impiego Involontaria</div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div>Indennità mensile assicurata</div>
                                </div>
                            </div>

                            <!-- Pacchetto 4 -->
                            <div class="grid grid-cols-3 hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-4"
                                        :value="4"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-4">
                                        <strong>Pacchetto 4</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Lavoratori Autonomi<br>Lavoratori Dipendenti Pubblici<br>Non Lavoratori</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div class="mb-2">Inabilità Totale Temporanea<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                    <div>Ricovero Ospedaliero<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div class="mb-2">Indennità mensile assicurata</div>
                                    <div>Indennità mensile assicurata</div>
                                </div>
                            </div>
                        </div>

                        <h2 class="mb-4 mt-14">Durata del contratto</h2>

                        <div v-if="form.pacchetto">
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.durata" inputId="durata-5" name="durata" value="5" :disabled="!form.pacchetto" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="durata-5">5 anni</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.durata" inputId="durata-10" name="durata" value="10" :disabled="!form.pacchetto || (form.pacchetto === 3 || form.pacchetto === 4)" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="durata-10" :class="{'cursor-not-allowed opacity-50': (form.pacchetto === 3 || form.pacchetto === 4)}">10 anni (solo in caso di scelta del Pacchetto 1 o del Pacchetto 2)</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="durata">Seleziona la durata</FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Premio unico anticipato per tutte le garanzie del Pacchetto e per tutta la durata prescelta</h2>
                        <div>
                            <InputNumber
                                v-model="form.premioUnico"
                                placeholder="Premio unico"
                                mode="currency"
                                currency="EUR"
                                locale="it-IT"
                                class="w-3/6"
                            />
                            <FormMessage :errors="form.errors" field="premioUnico">
                                Importo di premio unico anticipato
                                (Incluse imposte pari al 2.5% - Garanzia Decesso
                                esente
                            </FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Mezzi di pagamento del premio</h2>

                        <div class="mb-8">
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-1" name="modalitaPagamento" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="modalita-1">Assegni bancari, postali o circolari, muniti della clausola di non trasferibilità, intestati a SIMPLY BROKER S.R.L.</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-2" name="modalitaPagamento" value="2" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="modalita-2">Ordini di bonifico che abbiano quale beneficiario SIMPLY BROKER S.R.L.</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-3" name="modalitaPagamento" value="3" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="modalita-3">POS (Pagobancomat) messo a disposizione da SIMPLY BROKER S.R.L</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="modalitaPagamento">Seleziona una modalità</FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Informazioni sullo stato di salute</h2>

                        <AxaHealthQuestionnaire :healthData="form.healthData" :errors="{messages: form.errors, key: 'healthData'}" />

                        <h2 class="mb-4 mt-14">Nomina dei beneficiari in caso di decesso dell'aderente/assicurato</h2>

                        <div class="mb-8">
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiario" inputId="nomina" name="beneficiario" value="nomina" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="nomina">Designo nominativamente il/i seguente/i soggetto/i quale beneficiario/i della prestazione</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiario" inputId="eredi" name="beneficiario" value="eredi" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="eredi">Designo genericamente quali beneficiari della prestazione i miei eredi (legittimi e/o testamentari)</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="beneficiario">Seleziona una modalità</FormMessage>
                        </div>

                        <div class="mb-8" v-if="form.beneficiario === 'nomina'">
                            <div class="font-semibold leading-6 text-gray-900 mb-3">Beneficiario nominato n. 1</div>
                            <div class="grid grid-cols-2 gap-x-5 gap-y-6">
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[0].nome" placeholder="Nome" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.nome">Inserisci il nome.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[0].cognome" placeholder="Cognome" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.cognome">Inserisci il cognome.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" pattern="^[a-zA-Z]{6}[0-9]{2}[ABCDEHLMPRSTabcdehlmprst][0-9]{2}[a-zA-Z][0-9]{3}[a-zA-Z]$" v-model="form.beneficiarioNomine[0].cf" placeholder="Codice fiscale" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.cf">Inserisci il codice fiscale.</FormMessage>
                                </div>
                                <div class="col-span-2">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[0].indirizzo" placeholder="Indirizzo" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.indirizzo">Inserisci l'indirizzo.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[0].telefono" placeholder="Telefono" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.telefono">Inserisci il telefono.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[0].email" placeholder="Email" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.0.email">Inserisci il email.</FormMessage>
                                </div>
                            </div>
                            <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">Relazione con l’Aderente/Assicurato</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiarioNomine[0].relazione" inputId="parentela-1" value="parentela" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="parentela-1">Nucleo familiare (rapporto di parentela, coniugio, unione civile, convivenza more uxorio)</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiarioNomine[0].relazione" inputId="eredi" name="altro-1" value="altro" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="altro-1">Altro</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="beneficiarioNomine.0.relazione">Seleziona una relazione</FormMessage>
                            <div class="mt-5">
                                <div class="flex items-center">
                                    <Checkbox
                                        v-model="form.beneficiarioNomine[0].noComunicazione"
                                        :binary="true"
                                        inputId="noComunicazione-1"
                                        class="cursor-pointer"
                                    />
                                    <label
                                        for="noComunicazione-1"
                                        class="ml-2 text-gray-600 cursor-pointer select-none"
                                    >
                                        Barrare la seguente casella se si desidera escludere l’invio di comunicazioni da parte dell’Impresa al beneficiario, prima dell’evento Decesso dell’Assicurato
                                    </label>
                                </div>
                            </div>

                            <hr class="my-4">

                            <div class="font-semibold leading-6 text-gray-900 mb-3">Beneficiario nominato n. 2</div>
                            <div class="grid grid-cols-2 gap-x-5 gap-y-6">
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[1].nome" placeholder="Nome" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.nome">Inserisci il nome.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[1].cognome" placeholder="Cognome" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.cognome">Inserisci il cognome.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" pattern="^[a-zA-Z]{6}[0-9]{2}[ABCDEHLMPRSTabcdehlmprst][0-9]{2}[a-zA-Z][0-9]{3}[a-zA-Z]$" v-model="form.beneficiarioNomine[1].cf" placeholder="Codice fiscale" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.cf">Inserisci il codice fiscale.</FormMessage>
                                </div>
                                <div class="col-span-2">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[1].indirizzo" placeholder="Indirizzo" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.indirizzo">Inserisci l'indirizzo.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[1].telefono" placeholder="Telefono" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.telefono">Inserisci il telefono.</FormMessage>
                                </div>
                                <div class="col-span-1">
                                    <InputText class="w-full" v-model="form.beneficiarioNomine[1].email" placeholder="Email" />
                                    <FormMessage :errors="form.errors" field="beneficiarioNomine.1.email">Inserisci il email.</FormMessage>
                                </div>
                            </div>
                            <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">Relazione con l’Aderente/Assicurato</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiarioNomine[1].relazione" inputId="parentela-2" value="parentela" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="parentela-2">Nucleo familiare (rapporto di parentela, coniugio, unione civile, convivenza more uxorio)</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.beneficiarioNomine[1].relazione" inputId="eredi" name="altro-2" value="altro" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="altro-2">Altro</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="beneficiarioNomine.1.relazione">Seleziona una relazione</FormMessage>
                            <div class="mt-5">
                                <div class="flex items-center">
                                    <Checkbox
                                        v-model="form.beneficiarioNomine[1].noComunicazione"
                                        :binary="true"
                                        inputId="noComunicazione-2"
                                        class="cursor-pointer"
                                    />
                                    <label
                                        for="noComunicazione-2"
                                        class="ml-2 text-gray-600 cursor-pointer select-none"
                                    >
                                        Barrare la seguente casella se si desidera escludere l’invio di comunicazioni da parte dell’Impresa al beneficiario, prima dell’evento Decesso dell’Assicurato
                                    </label>
                                </div>
                            </div>

                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900 mb-3">Indicazione referente terzo (opzionale)</div>
                            <PersonalData :personalData="form.referenteTerzo" :errors="{messages: form.errors, key: 'referenteTerzo'}" />
                        </div>

                        <h2 class="mb-4 mt-14">Modulo per l’identificazione e l’adeguata verifica della clientela</h2>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow ">Dichiaro di essere una Persona Politicamente Esposta</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.PEP" inputId="pep-1" name="PEP" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="pep-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.PEP" inputId="pep-0" name="PEP" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="pep-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="PEP"></FormMessage>
                        <div v-if="form.PEP === '1'">
                            <InputText class="w-full" v-model="form.PEPType" placeholder="Tipologia PEP" />
                            <FormMessage :errors="form.errors" field="PEPType">Inserisci la tipologia PEP.</FormMessage>
                        </div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow ">Dichiaro di essere destinatario di misure di congelamento dei fondi e risorse economiche</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.congelamentoFondi" inputId="cong-1" name="congelamentoFondi" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="cong-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.congelamentoFondi" inputId="cong-0" name="congelamentoFondi" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="cong-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="congelamentoFondi"></FormMessage>
                        <div v-if="form.congelamentoFondi === '1'">
                            <InputText class="w-full" v-model="form.congelamentoFondiMotivo" placeholder="Motivo congelamento fondi" />
                            <FormMessage :errors="form.errors" field="congelamentoFondiMotivo">Indicare il motivo.</FormMessage>
                        </div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow ">Dichiaro di essere/non essere sottoposto a procedimenti o di aver/non aver subito condanne per reati in materia economica/finanziaria/tributaria/societaria</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.precedentiPenali" inputId="prec-1" name="precedentiPenali" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="prec-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.precedentiPenali" inputId="prec-0" name="precedentiPenali" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="prec-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="precedentiPenali"></FormMessage>
                        <div v-if="form.precedentiPenali === '1'">
                            <InputText class="w-full" v-model="form.precedentiPenaliMotivo" placeholder="Motivo precedenti penali" />
                            <FormMessage :errors="form.errors" field="congelamentoFondiMotivo">Indicare il motivo.</FormMessage>
                        </div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow ">Dichiaro ai fini dell'identificazione del Titolare Effettivo, di essere una persona fisica che agisce in nome
                                e per conto proprio, di essere il soggetto Assicurato, e quindi che non esiste il titolare effettivo</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.personaFisica" inputId="fisica-1" name="personaFisica" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="fisica-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.personaFisica" inputId="fisica-0" name="personaFisica" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="fisica-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="personaFisica"></FormMessage>

                        <div class=" mb-3">Fornisco, con riferimento allo scopo e alla natura prevista del rapporto continuativo, le seguenti informazioni: </div>
                        <div class="mb-3">
                            Tipologia di rapporto continuativo (informazione immediatamente desunta dal rapporto):
                            <div class="italic">Stipula di un contratto di assicurazione di puro rischio che prevede garanzie di ramo vita : Polizza Income
                                Protection, garanzie per il caso di decesso / inabilità temporanea totale / ricovero ospedaliero (SIMPLY
                                PROTECTION)</div>
                        </div>

                        <div class="mb-3">
                            Scopo prevalente del rapporto continuativo in riferimento alle garanzie vita (informazione immediatamente desunta dal rapporto):
                            <div class="italic">Protezione assicurativa al fine di garantire ai beneficiari un indennizzo qualora si verifichi l’evento oggetto di copertura</div>
                        </div>

                        <div :class="form.origineFondi === 'altro' ? 'mb-3' : 'mb-8'">
                            <div>Origine dei fondi utilizzati per il pagamento dei premi assicurativi:</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.origineFondi" inputId="proprie" name="origineFondi" value="proprie" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="proprie">Proprie risorse economiche</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.origineFondi" inputId="eredita" name="origineFondi" value="eredita" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="eredita">Eredità</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.origineFondi" inputId="donazione" name="origineFondi" value="donazione" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="donazione">Donazione</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.origineFondi" inputId="altro" name="origineFondi" value="altro" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="altro">Altro</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="origineFondi">Seleziona una modalità</FormMessage>
                        </div>

                        <div class=" mb-5" v-if="form.origineFondi === 'altro'">
                            <InputText class="w-full" v-model="form.origineFondiAltro" placeholder="Nome" />
                            <FormMessage :errors="form.errors" field="origineFondiAltro">Inserisci l'origine dei fondi.</FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Questionario per la valutazione delle richieste ed esigenze assicurative</h2>

                        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">INFORMAZIONI SULLE RICHIESTE ED ESIGENZE ASSICURATIVE</div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Hai richiesto un prodotto assicurativo che possa tutelare i tuoi familiari a carico o altri soggetti con una copertura che garantisca loro
                                un capitale in caso di tuo decesso?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze2_1" inputId="esigenze2_1-1" name="esigenze2_1" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze2_1-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze2_1" inputId="esigenze2_1-0" name="esigenze2_1" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze2_1-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze2_1"></FormMessage>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Intendi altresì perseguire obiettivi di protezione assicurativa con altre garanzie quali la perdita d’impiego involontaria (qualora tu sia
                                un lavoratore dipendente a tempo indeterminato) oppure l’inabilità totale temporanea ed il ricovero ospedaliero (qualora tu sia un
                                lavoratore dipendente pubblico, un lavoratore autonomo oppure un non-lavoratore), che ti garantiscano delle indennità mensili in
                                caso di sinistro?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze2_2" inputId="esigenze2_2-1" name="esigenze2_2" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze2_2-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze2_2" inputId="esigenze2_2-0" name="esigenze2_2" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze2_2-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze2_2"></FormMessage>

                        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">INFORMAZIONI SULL’ESISTENZA DI ALTRE COPERTURE ASSICURATIVE</div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Confermi di non aver già sottoscritto altre assicurazioni relative a tutti o ad alcuni dei rischi sopra indicati, sulla persona, ad oggi ancora
                                operanti e la cui prestazione corrisponda al capitale assicurato, oppure ad uno o più indennizzi mensili (in base al capitale assicurato ed
                                alle indennità mensili da te scelte nel modulo di adesione)?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze3" inputId="esigenze3-1" name="esigenze3" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze3-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze3" inputId="esigenze3-0" name="esigenze3" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze3-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze3"></FormMessage>

                        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">ASPETTATIVE SULLA DURATA DEL CONTRATTO</div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Confermi di essere interessato ad una protezione di durata poliennale (5 anni oppure - solo in caso di scelta del Pacchetto 1 o del
                                Pacchetto 2 - 10 anni) a tua scelta, successivamente non rinnovabile alla scadenza?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze4" inputId="esigenze4-1" name="esigenze4" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze4-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze4" inputId="esigenze4-0" name="esigenze4" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze4-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze4"></FormMessage>

                        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">ASPETTATIVE SULLA TIPOLOGIA DI PREMIO PREVISTA</div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Il contratto assicurativo che intendi stipulare prevede all’adesione il pagamento di un premio unico anticipato per l'intera durata
                                poliennale del contratto (pari a 5 anni - oppure, solo in riferimento ai pacchetti 1 e 2, pari a 10 anni). Ritieni tale tipologia di pagamento
                                del premio, nonché il suo ammontare, in linea con le tue aspettative?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze5" inputId="esigenze5-1" name="esigenze5" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze5-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze5" inputId="esigenze5-0" name="esigenze5" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze5-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze5"></FormMessage>

                        <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">INFORMAZIONI SULLE LIMITAZIONI DELLE COPERTURE (ESCLUSIONI, CARENZE, MASSIMALI)</div>

                        <div class="flex items-center gap-5 mb-2">
                            <div class="flex-grow">Il contratto prevede esclusioni, carenze, massimali ed altre limitazioni che possono dar luogo al mancato pagamento dell’indennizzo.
                                Sei informato sull’esistenza di tali clausole contrattuali e ritieni comunque le prestazioni previste dal contratto coerenti con le tue
                                esigenze di protezione?</div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze6" inputId="esigenze6-1" name="esigenze6" value="1" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze6-1">Si</label>
                            </div>
                            <div class="flex items-center gap-1">
                                <RadioButton v-model="form.esigenze6" inputId="esigenze6-0" name="esigenze6" value="0" />
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="esigenze6-0">No</label>
                            </div>
                        </div>
                        <FormMessage :errors="form.errors" field="esigenze6"></FormMessage>

                        <div class="mt-6 flex justify-center">
                            <Button type="submit" icon="pi pi-save" label="Salva" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
